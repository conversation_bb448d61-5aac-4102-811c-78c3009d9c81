import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import deLocale from "@fullcalendar/core/locales/de";
import enLocale from "@fullcalendar/core/locales/en-gb";
import frLocale from "@fullcalendar/core/locales/fr";
import nlLocale from "@fullcalendar/core/locales/nl";
import {
  DatesSetArg,
  EventChangeArg,
  EventClickArg,
  EventContentArg,
  LocaleInput,
} from "@fullcalendar/core";
import queryString from "query-string";
import { useRecoilState } from "recoil";
import {
  getCurrentUser,
  useCheckVehicleSlotAvailability,
  useReservation,
  useUpdateReservation,
  useVehiclesWithReservations,
} from "src/common/api";
import { CePaper } from "src/common/components";
import {
  RESERVATION_CANCEL_DEFAULT,
  RESERVATION_EDIT_DEFAULT,
  RESERVATION_FORM_VALUES,
} from "src/common/constants";
import {
  reservationCancelValuesState,
  reservationEditValuesState,
  reservationFormValuesState,
  reservationFullFormValuesState,
  themeState,
  vehicleInformationFlowState,
} from "src/common/state";
import {
  JobStatus,
  ManagerReservationEvent,
  ReservationFormValues,
  ReservationQueryFilters,
  UpdateReservationDto,
  Vehicle,
} from "src/common/types";
import {
  determineCancelationFee,
  reservationPayloadIntoFormData,
  transformVehicleWithReservationsIntoResources,
  turnReservationIntoFormValues,
} from "src/common/utils";
import FullCalendar from "@fullcalendar/react";
import { useTranslation } from "react-i18next";
import dayGridPlugin from "@fullcalendar/daygrid";
import listPlugin from "@fullcalendar/list";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import interactionPlugin from "@fullcalendar/interaction";
import {
  endOfDay,
  endOfWeek,
  format,
  formatISO,
  parseISO,
  startOfDay,
  startOfWeek,
  sub,
} from "date-fns";
import { zonedTimeToUtc } from "date-fns-tz";

import { formatCalendarDate } from "src/common/utils/formatDate";
import { calendarLocales } from "src/i18n";
import EventContentDay from "./event-content/EventContentDay";
import { useUpdateJob } from "src/common/api/job";
import { ReservationCancelWithCommentModal } from "src/reservations/ReservationCancelWithCommentModal";
import { VehicleInformationModal } from "src/vehicles/vehicleInfoModal/VehicleInformationModal";
import { EventImpl } from "@fullcalendar/core/internal";
import { ReservationModal } from "src/reservations/ReservationModal";
import { useNavigate } from "react-router-dom";

import { ReservationEditModal } from "src/reservations/ReservationEditModal";
import EventContentList from "src/dispatcherPlanning/list-view/ListView";
import ResourceLabelContent from "src/dispatcherPlanning/resource-label-content/ResourceLabelContent";
import EventContentModal from "src/dispatcherPlanning/event-content/EventContentModal";
import { ConfirmReservationModal } from "src/dispatcherPlanning/ConfirmReservationModal";
import toast from "react-hot-toast";
import { useCompany } from "src/common/api/company";
import { ExistingFilterFormValues } from "src/search/form/FilterQuickForm";
import { PlanningFilters } from "./PlanningFilters";
import { PlanningFilterValues } from "src/common/types/planning";
import { PLANNING_FILTERS_DEFAULTS } from "src/common/constants/planning";
import { generatePlanningFilterOptions } from "src/common/utils/planning";
import { UnavailablePeriodPlanning } from "src/common/types";

export const Planning = () => {
  const calendarRef = useRef<FullCalendar | null>(null);
  const localeText: { [key: string]: LocaleInput } = {
    en: enLocale,
    fr: frLocale,
    nl: nlLocale,
    de: deLocale,
  };

  const [theme] = useRecoilState(themeState);
  const [vehicleInformationValues, setVehicleInformationValues] =
    useRecoilState(vehicleInformationFlowState);

  const [reservationCancelValues, setReservationCancelValues] = useRecoilState(
    reservationCancelValuesState
  );
  const [reservationEditValues, setReservationEditValues] = useRecoilState(
    reservationEditValuesState
  );
  const [reservationFormValues, setReservationFormValues] = useRecoilState(
    reservationFormValuesState
  );

  const [reservationFullFormValues, setReservationFullFormValues] =
    useRecoilState(reservationFullFormValuesState);

  const navigate = useNavigate();
  const isDarkMode = theme === "dark";
  const { t, i18n } = useTranslation(["manager", "common", "dispatcher"]);
  const selectedLanguage = i18n.language;
  const currentUser = getCurrentUser();
  const managerIds = currentUser?.id ? [currentUser.id] : [];

  const [events, setEvents] = useState<ManagerReservationEvent[]>([]);
  const [selectedReservationId, setSelectedReservationId] = useState<
    number | null
  >(null);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [pendingReservationData, setPendingReservationData] =
    useState<UpdateReservationDto>();
  const [openJobEventModal, setOpenJobEventModal] = useState(false);

  const [filters, setFilters] = useState<ExistingFilterFormValues>({
    dateFrom: null,
    dateTo: null,
    type: null,
    siteAddress: null,
    coordinates: null,
    radius: 50,
    boomSize: null,
    pipeDiameter: null,
    requiredPipes: null,
  });

  const [planningFilters, setPlanningFilters] = useState<PlanningFilterValues>(
    PLANNING_FILTERS_DEFAULTS
  );

  const initialStartDate = startOfWeek(new Date(), { weekStartsOn: 1 });
  const initialEndDate = endOfWeek(new Date(), { weekStartsOn: 1 });
  const [dateRange, setDateRange] = useState<{
    dateFrom: string;
    dateTo: string;
  }>({
    dateFrom: zonedTimeToUtc(initialStartDate, "UTC").toISOString(),
    dateTo: zonedTimeToUtc(initialEndDate, "UTC").toISOString(),
  });
  const vehiclesQueryParams = useMemo(() => {
    const params = {
      sortBy: "vehicle.boomSize",
      sortDir: "ASC" as const,
      searchType: "All" as const,
      dateFrom: planningFilters.dateFrom 
        ? new Date(planningFilters.dateFrom).toISOString() 
        : dateRange.dateFrom,
      dateTo: planningFilters.dateTo 
        ? new Date(planningFilters.dateTo).toISOString() 
        : dateRange.dateTo,
      type: planningFilters.type || filters.type,
      siteAddress: planningFilters.siteAddress || filters.siteAddress,
      boomSize: planningFilters.boomSize,
      operatorName: planningFilters.operator || undefined,
      clientName: planningFilters.customer || undefined,
    };
    return params;
  }, [
    planningFilters.dateFrom,
    planningFilters.dateTo,
    planningFilters.type,
    planningFilters.siteAddress,
    planningFilters.boomSize,
    planningFilters.operator,
    planningFilters.customer,
    dateRange.dateFrom,
    dateRange.dateTo,
    filters.type,
    filters.siteAddress,
  ]);

  const {
    data: allVehiclesWithReservations,
    isLoading: isFetchingVehiclesWithReservations,
    isSuccess: isVehiclesWithReservationsSuccess,
  } = useVehiclesWithReservations(
    vehiclesQueryParams,
    Boolean(managerIds.length)
  );

  const { data: reservation, isLoading: isReservationLoading } = useReservation(
    selectedReservationId!,
    Boolean(selectedReservationId)
  );
  const {
    data: companyData,
    isLoading: isCompanyLoading,
    isSuccess: isCompanySuccess,
  } = useCompany(currentUser?.companyId, Boolean(currentUser?.companyId));

  const {
    mutate: handleUpdateJob,
    isLoading: isUpdatingJob,
    isSuccess: isJobUpdated,
  } = useUpdateJob();

  const {
    mutate: handleUpdateReservation,
    isLoading: isUpdateReservationLoading,
  } = useUpdateReservation();

  const {
    mutateAsync: handleCheckVehicleAvailabilityAsync,
    isLoading: isCheckVehicleAvailabilityLoading,
  } = useCheckVehicleSlotAvailability();

  const isLoading =
    isFetchingVehiclesWithReservations ||
    isCheckVehicleAvailabilityLoading ||
    isReservationLoading ||
    isUpdatingJob ||
    isUpdateReservationLoading;

  const vehiclesWithReservations = useMemo(
    () => allVehiclesWithReservations?.data || [],
    [allVehiclesWithReservations]
  );

  const filterOptions = useMemo(() => {
    return generatePlanningFilterOptions(
      vehiclesWithReservations,
    );
  }, [vehiclesWithReservations]);

  const [firstLoad, setFirstLoad] = useState(true);

  useEffect(() => {
    if (firstLoad && calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();

      if (calendarApi) {
        calendarApi.changeView("resourceTimelineWeek");
        window.dispatchEvent(new Event("resize"));
      }
    }
  }, [firstLoad, calendarRef.current]);

  const filteredVehiclesWithReservations = vehiclesWithReservations;

  useEffect(() => {
    if (
      filteredVehiclesWithReservations.length &&
      isVehiclesWithReservationsSuccess
    ) {
      const mappedEvents: ManagerReservationEvent[] =
        filteredVehiclesWithReservations.flatMap((vehicle) =>
          vehicle.reservations.map((reservation) => ({
            id: reservation.id.toString(),
            resourceId: vehicle.id.toString(),
            title: reservation.clientDetails && reservation.clientDetails.name && reservation.clientDetails.lastName
              ? `${reservation.clientDetails.name} ${reservation.clientDetails.lastName}` 
              : vehicle.companyName,
            start: reservation.dateFrom || "",
            end: reservation.dateTo || "",
            editable: reservation.ownReservation,
            extendedProps: {
              schedule:
                reservation.dateFrom && reservation.dateTo
                  ? `${format(
                      new Date(reservation.dateFrom),
                      "HH:mm"
                    )} - ${format(new Date(reservation.dateTo), "HH:mm")}`
                  : "",
              from: reservation.dateFrom
                ? `${format(new Date(reservation.dateFrom), "HH:mm")}`
                : "",
              scheduleFrom: reservation.dateFrom
                ? `${format(new Date(reservation.dateFrom), "dd/MM - HH:mm")}`
                : "",
              scheduleTo: reservation.dateTo
                ? `${format(new Date(reservation.dateTo), "dd/MM - HH:mm")}`
                : "",
              address: reservation.siteAddress || "",
              city: reservation.city,
              quantity: reservation.amount || 0,
              type: "job",
              jobStatus: reservation.jobStatus || "",
              ownReservation: reservation.ownReservation,
              hasBeenRead: reservation.hasBeenRead,
              dispatcherId: reservation.dispatcherId,
              flowRate: reservation.flowRate || 0,
            },
          }))
        );
      const mappedUnavailableEvents: ManagerReservationEvent[] =
        filteredVehiclesWithReservations.flatMap((vehicle) =>
          (vehicle.unavailablePeriods || []).map((period: UnavailablePeriodPlanning) => ({
            id: `unavailable-${vehicle.id}-${period.id}`,
            resourceId: vehicle.id.toString(),
            start: formatISO(parseISO(period.from)),
            end: formatISO(parseISO(period.to)),
            display: "background",
            backgroundColor: "red",
          }))
        );
      setEvents([...mappedEvents, ...mappedUnavailableEvents]);
    }
  }, [filteredVehiclesWithReservations, isVehiclesWithReservationsSuccess]);

  const handleVehicleInformationModal = (vehicleId: number) => {
    setVehicleInformationValues({
      vehicleId,
      flow: "Vehicle Details",
    });
  };

  const updateDateRange = (start: Date, end: Date) => {
    const localStart = startOfDay(start);
    const localEnd = endOfDay(end);
    const newDateRange = {
      dateFrom: zonedTimeToUtc(localStart, "UTC").toISOString(),
      dateTo: zonedTimeToUtc(localEnd, "UTC").toISOString(),
    };
    setDateRange(newDateRange);
  };

  const handleDateClick = (args: DatesSetArg) => {
    const { type } = args.view;
    let start, end;
    switch (type) {
      case "resourceTimelineDay":
        start = end = startOfDay(args.start);

        break;

      case "resourceTimelineWeek":
        start = startOfWeek(args.start, { weekStartsOn: 1 });
        end = endOfWeek(args.start, { weekStartsOn: 1 });

        break;

      default:
        start = startOfDay(args.start);
        end = endOfDay(args.end);
    }

    if (start && end && !firstLoad) {
      updateDateRange(start, end);
    }
  };
  const resources = transformVehicleWithReservationsIntoResources(
    filteredVehiclesWithReservations
  );
  useEffect(() => {
    if (isJobUpdated) {
      setReservationCancelValues(RESERVATION_CANCEL_DEFAULT);
    }
  }, [isJobUpdated, setReservationCancelValues]);

  const dayEventContent = (arg: EventContentArg) => {
    if (arg.event.display === "background") {
      return null;
    }
    const argDate = arg.event.start?.toLocaleDateString("en-CA");
    const argEventVehicle = arg.event._def.resourceIds?.[0];
    const eventsArray = events.filter((event) => {
      const eventDate = new Date(event.start).toLocaleDateString("en-CA");
      return eventDate === argDate && event.resourceId === argEventVehicle;
    });
    return <EventContentDay arg={arg} eventsNumber={eventsArray.length} />;
  };

  const listMonthContent = (arg: EventContentArg) => {
    if (arg.event.display === "background") {
      return null;
    }
    const resource = resources.find(
      (resource) => resource?.id === arg.event?._def?.resourceIds?.[0]
    );
    return (
      <EventContentList
        arg={arg}
        resource={resource || null}
        handleVehicleInformationModal={handleVehicleInformationModal}
      />
    );
  };

  const handleEventClick = (arg: EventClickArg) => {
    if (arg.event.extendedProps.type === "unavailable-period") {
      // handleOpenInfoUnavailablePeriodModal(arg);
    }
    const { ownReservation, type } = arg.event.extendedProps;
    if (!ownReservation) {
      return;
    }
    if (type === "job") {
      setSelectedReservationId(Number(arg.event.id));
      setOpenJobEventModal(true);
    }
  };

  const cancelationFee = determineCancelationFee(
    reservation?.dateFrom || "",
    companyData?.settings.cancellation?.cancellationWindow || "",
    companyData?.settings.cancellation?.reducedCancellationWindow || "",
    reservation?.pricelist?.cancellationFee || 0,
    reservation?.pricelist?.dayCancellationFee || 0
  );

  const handleOpenReservationModalCancel = () => {
    setReservationCancelValues({
      reservationId: reservation?.id || null,
      jobId: reservation?.job?.id || null,
      reservationTitle: reservation?.orderNumber || "",
      cancelationFee,
      flow: "Cancel",
    });
  };

  const handleCloseJobEventModal = () => {
    setOpenJobEventModal(false);
    window.dispatchEvent(new Event("resize"));
  };

  const confirmJobCancelled = () => {
    return reservation?.job?.status === "CANCELLED" || false;
  };

  const openEditReservationModal = () => {
    setReservationEditValues({
      reservationId: reservation?.id || null,
      flow: "Edit",
      dateFrom: reservation?.dateFrom || "",
      dateTo: reservation?.dateTo || "",
      siteAddress: reservation?.siteAddress || "",
      city: reservation?.city || "",
      plz: reservation?.plz || null,
      vehicle: reservation?.vehicle || null,
    });
  };

  const handleCloseReservationModalCancel = () => {
    if (!isLoading) {
      setReservationCancelValues(RESERVATION_CANCEL_DEFAULT);
    }
  };

  const handleCancelReservation = (comment: string) => {
    handleUpdateJob({
      status: JobStatus.CANCELLED,
      jobId: Number(reservationCancelValues.jobId),
      reservationId: Number(reservationCancelValues.reservationId),
      comments: comment,
    });
  };

  const handleCloseReservationModal = () => {
    if (!isLoading) {
      setReservationFormValues(RESERVATION_FORM_VALUES);
    }
  };

  const handleNavigateToReservationDetails = () => {
    navigate(`/reservation/${reservation?.id}`);
  };

  const handleEditReservation = (values: ReservationFormValues) => {
    if (reservation) {
      const reservationQuery: ReservationQueryFilters = {
        dateFrom: values.dateFrom
          ? new Date(values.dateFrom).toISOString()
          : null,
        dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : null,
        siteAddress: values.siteAddress || null,
        city: values.city || null,
        plz: values.plz || null,
        lat: values.location?.coordinates[0] || null,
        lng: values.location?.coordinates[1] || null,
        vehicleId: reservation.vehicle?.id.toString() || null,
        editMode: true,
      };

      const queryParams = queryString.stringify(reservationQuery);
      const reservationFormData = turnReservationIntoFormValues(reservation);
      setReservationFullFormValues(reservationFormData);
      navigate(`/reservation?${queryParams}`);
      handleCloseReservationEditModal();
    }
  };

  const handleCloseConfirmModal = () => {
    if (pendingReservationData) {
      const eventToRestore = events.find(
        (event) => event.id === pendingReservationData.reservationId.toString()
      );

      if (eventToRestore) {
        const originalStart = eventToRestore.start;
        const originalEnd = eventToRestore.end;

        clearEventSlot({
          id: eventToRestore.id,
          start: new Date(originalStart),
          end: new Date(originalEnd),
        } as EventImpl);
      }
    }
    setIsConfirmModalOpen(false);
  };

  const handleConfirmReservation = () => {
    if (pendingReservationData) {
      const formData = reservationPayloadIntoFormData(pendingReservationData);
      handleUpdateReservation({
        reservationId: pendingReservationData.reservationId,
        updateReservationArgs: formData,
      });
    }
    handleCloseConfirmModal();
  };

  const clearEventSlot = (event: EventImpl): void => {
    const eventList = [...events];
    const currentEvent = eventList.find((ev) => ev.id === event.id);

    if (currentEvent) {
      const index = eventList.findIndex((ev) => ev.id === currentEvent.id);
      eventList[index] = {
        ...currentEvent,
        start: event.start?.toISOString() || "",
        end: event.end?.toISOString() || "",
      };

      setEvents(eventList);
    }

    return;
  };

  const eventChange = async (droppedColumn: EventChangeArg) => {
    const event = droppedColumn.event;
    const oldEvent = droppedColumn.oldEvent;
    const currentTime = new Date();

    const reservationStatus = event.extendedProps.jobStatus;

    if (reservationStatus !== "NOT_STARTED") {
      return clearEventSlot(oldEvent);
    }

    if (event.start && event.start < currentTime) {
      return clearEventSlot(oldEvent);
    }

    if (event.end && event.end < currentTime) {
      return clearEventSlot(oldEvent);
    }

    const newVehicleResources = event.getResources();
    const newResourcesProps = newVehicleResources.length
      ? newVehicleResources[0]?.extendedProps
      : null;

    const oldVehicleResources = oldEvent.getResources();
    const oldResourcesProps = oldVehicleResources.length
      ? oldVehicleResources[0]?.extendedProps
      : null;

    const newVehicle = filteredVehiclesWithReservations.find(
      (vehicle) => vehicle.id === Number(newResourcesProps?.vehicleId)
    );

    const oldVehicle = filteredVehiclesWithReservations.find(
      (vehicle) => vehicle.id === Number(oldResourcesProps?.vehicleId)
    );

    const isSlotAvailable = await handleCheckVehicleAvailabilityAsync({
      id: newVehicle?.id!,
      currentReservationId: Number(event.id),
      dateFrom: event.start ? new Date(event.start).toISOString() : "",
      dateTo: event.end ? new Date(event.end).toISOString() : "",
    });

    if (!isSlotAvailable) {
      return clearEventSlot(oldEvent);
    }

    const sameVehicle = newVehicle?.id === oldVehicle?.id;
    const isSameSizeVehicle = newVehicle?.boomSize === oldVehicle?.boomSize;
    const isSameTypeVehicle = newVehicle?.type === oldVehicle?.type;

    if (sameVehicle || (isSameSizeVehicle && isSameTypeVehicle)) {
      const dispatcherId = event._def.extendedProps.dispatcherId;
      setPendingReservationData({
        reservationId: Number(event.id),
        dateFrom: event.start ? new Date(event.start).toISOString() : "",
        dateTo: event.end ? new Date(event.end).toISOString() : "",
        vehicleId: newVehicle?.id!,
        operatorId: newResourcesProps?.operator.id,
        managerId: newResourcesProps?.managerId,
        reservationType: "RESERVATION",
        dispatcherId,
      });
      setIsConfirmModalOpen(true);
    } else {
      return clearEventSlot(oldEvent);
    }
  };

  const handleSubmitReservationModal = async (
    values: ReservationFormValues
  ) => {
    const isSlotAvailable = await handleCheckVehicleAvailabilityAsync({
      id: values.vehicle?.id!,
      currentReservationId: Number(values.reservationId),
      dateFrom: values.dateFrom ? new Date(values.dateFrom).toISOString() : "",
      dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : "",
    });

    if (!isSlotAvailable) {
      toast.error(t("common:slot-not-available"));
      return;
    }

    const reservationQuery: ReservationQueryFilters = {
      dateFrom: values.dateFrom
        ? new Date(values.dateFrom).toISOString()
        : null,
      dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : null,
      siteAddress: values.siteAddress || null,
      vehicleId: values.vehicle?.id.toString() || null,
      city: values.city || null,
      plz: values.plz || null,
      lat: values.location?.coordinates[0] || null,
      lng: values.location?.coordinates[1] || null,
    };
    const queryParams = queryString.stringify(reservationQuery);

    setReservationFormValues(RESERVATION_FORM_VALUES);

    navigate(`/reservation?${queryParams}`);
  };

  const handleCloseReservationEditModal = () => {
    if (!isLoading) {
      setReservationEditValues(RESERVATION_EDIT_DEFAULT);
    }
  };

  const handlePlanningFiltersChange = useCallback(
    (newFilters: PlanningFilterValues) => {
      setPlanningFilters(newFilters);
      if ((newFilters.dateFrom || newFilters.dateTo) && calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();
        const currentView = calendarApi.view.type;

        if (currentView !== "listWeek") {
          calendarApi.changeView("listWeek");
        }
      }
    },
    []
  );

  const clearPlanningFilters = useCallback((): void => {
    setPlanningFilters(PLANNING_FILTERS_DEFAULTS);
  }, []);

  return (
    <>
      <PlanningFilters
        filters={planningFilters}
        onFiltersChange={handlePlanningFiltersChange}
        onClearFilters={clearPlanningFilters}
        filterOptions={filterOptions}
        isLoading={isLoading}
        hideFields={["searchCategory", "companies"]}
        calendarRef={calendarRef}
      />

      <CePaper
        className={isDarkMode ? "dark-mode" : ""}
        sx={{
          background: (theme) => theme.palette.background.paper,
          boxShadow: "0px 1px 3px 0px #0000001F",
          p: 2,
          mb: 2,
        }}
      >
        <FullCalendar
          ref={calendarRef}
          selectable={true}
          firstDay={1}
          nowIndicatorDidMount={() => {
            if (firstLoad) {
              setFirstLoad(false);
            }
          }}
          datesSet={handleDateClick}
          eventChange={async (droppedColumn) => eventChange(droppedColumn)}
          select={(selectionInfo) => {
            if (selectionInfo.resource) {
              const vehicleModel = filteredVehiclesWithReservations.find(
                (vehicle) => vehicle.id === Number(selectionInfo.resource?.id)
              );

              const selectionStart = parseISO(selectionInfo.startStr);
              const selectionEnd = parseISO(selectionInfo.endStr);

              const isUnavailable = vehicleModel?.unavailablePeriods?.filter(
                (period: UnavailablePeriodPlanning) => {
                  const periodFrom = parseISO(period.from);
                  const periodTo = endOfDay(parseISO(period.to));
                  return selectionStart < periodTo && selectionEnd > periodFrom;
                }
              );

              if (isUnavailable?.length) {
                const hasOperatorUnavailable = isUnavailable.some(
                  (period: UnavailablePeriodPlanning) => period.operatorId
                );

                const hasVehicleUnavailable = isUnavailable.some(
                  (period: UnavailablePeriodPlanning) => !period.operatorId
                );
                if (hasOperatorUnavailable) {
                  toast.error(t("common:operatorUnavailable"));
                }
                if (hasVehicleUnavailable) {
                  toast.error(t("common:vehicleUnavailable"));
                }
                return;
              }

              if (vehicleModel?.hasAssignedPricelist === false) {
                toast.error(t("common:vehicle-missing-pricelist"));
                return;
              }
              const vehicle = {
                id: vehicleModel?.id,
                name: vehicleModel?.vehicleUniqueId,
                operator: {
                  id: vehicleModel?.operatorId,
                  name: vehicleModel?.operatorName,
                },
              } as Vehicle;

              if (selectionInfo.view.type === "resourceTimelineDay") {
                const startTime = new Date(selectionInfo.startStr);

                const endTime = new Date(selectionInfo.endStr);

                setReservationFormValues({
                  ...reservationFormValues,
                  dateFrom: startTime.toString(),
                  dateTo: endTime.toString(),
                  vehicle,
                  hasAssignedPricelist: vehicleModel?.hasAssignedPricelist,
                  flow: "Add",
                });
              } else {
                const startTime = startOfDay(new Date(selectionInfo.startStr));

                const endDaySub = sub(new Date(selectionInfo.endStr), {
                  days: 1,
                });

                const endTime = endOfDay(endDaySub);

                setReservationFormValues({
                  ...reservationFormValues,
                  dateFrom: startTime.toString(),
                  dateTo: endTime.toString(),
                  vehicle,
                  hasAssignedPricelist: vehicleModel?.hasAssignedPricelist,
                  flow: "Add",
                });
              }
            }
          }}
          aspectRatio={3}
          dayHeaderDidMount={() =>
            setTimeout(() => window.dispatchEvent(new Event("resize")), 500)
          }
          editable={true}
          eventAllow={(_dropInfo, draggedEvent) =>
            draggedEvent?.extendedProps.ownReservation ?? false
          }
          eventClick={handleEventClick}
          eventDidMount={(arg) => {
            if (arg.event.display !== "background") {
              const eventElement = arg.el;
              eventElement.style.marginLeft = "5px";
              eventElement.style.marginRight = "5px";
            }
          }}
          events={(_fetchInfo, successCallback) => {
            const calendarApi = calendarRef.current?.getApi();

            const currentViewType = calendarApi?.view?.type;
            if (currentViewType === "listMonth") {
              const filteredEvents = events.filter((event) => {
                return event.extendedProps?.ownReservation;
              });
              successCallback(filteredEvents);
            } else {
              successCallback(events);
            }
          }}
          headerToolbar={{
            left: "prev,today,next",
            center: "title",
            right:
              "resourceTimelineDay,resourceTimelineWeek,resourceTimelineMonth,listMonth",
          }}
          height="auto"
          initialView="resourceTimelineDay"
          locale={localeText[selectedLanguage]}
          noEventsDidMount={() => window.dispatchEvent(new Event("resize"))}
          plugins={[
            dayGridPlugin,
            resourceTimelinePlugin,
            listPlugin,
            interactionPlugin,
          ]}
          resourceAreaWidth="260px"
          resourceOrder={"boomSize"}
          resourceLabelContent={(arg) => {
            return (
              <ResourceLabelContent
                handleVehicleInformationModal={handleVehicleInformationModal}
                arg={arg}
              />
            );
          }}
          resourceLaneDidMount={() => window.dispatchEvent(new Event("resize"))}
          eventStartEditable={true}
          eventResourceEditable={true}
          resources={resources}
          schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
          slotLabelDidMount={(arg) => {
            const columnHeaderCell = arg.el;
            columnHeaderCell.style.fontWeight = "lighter";
          }}
          nowIndicator={true}
          views={{
            resourceTimelineDay: {
              buttonText: t("common:day"),
              slotDuration: "00:30:00",
              slotLabelInterval: "01:00:00",
              slotLabelFormat: (arg) => {
                const startDate = arg.start.marker;
                const formattedDate = new Intl.DateTimeFormat("nl-BE", {
                  timeZone: "UTC",
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: false,
                }).format(startDate);
                return formattedDate;
              },
              eventContent: (arg: EventContentArg) => {
                if (arg.event.display === "background") {
                  return null;
                }
                return <EventContentDay arg={arg} eventsNumber={1} />;
              },
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
              slotMinWidth: 35,
              nowIndicator: true,
            },
            resourceTimelineWeek: {
              slotLabelInterval: { days: 1 },
              buttonText: t("common:week"),
              slotLabelFormat: (arg) =>
                formatCalendarDate(
                  arg.start.marker,
                  calendarLocales[selectedLanguage]
                ),

              eventContent: dayEventContent,
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
              nowIndicator: true,
            },
            resourceTimelineMonth: {
              type: "resourceTimeline",
              duration: { months: 1 },
              buttonText: t("common:month"),
              slotDuration: { days: 1 },
              slotLabelInterval: { days: 1 },
              slotLabelFormat: (arg) =>
                formatCalendarDate(
                  arg.start.marker,
                  calendarLocales[selectedLanguage]
                ),
              eventContent: dayEventContent,
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
              slotMinWidth: 150,
            },
            listMonth: {
              buttonText: t("common:list"),
              eventContent: listMonthContent,
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
            },
          }}
          dayHeaderFormat={{
            weekday: "long", // Display full day names like "Monday", "Tuesday", etc.
          }}
        />
      </CePaper>

      <VehicleInformationModal vehicleId={vehicleInformationValues.vehicleId} />

      <ReservationModal
        initialFormValues={reservationFormValues}
        isLoading={isLoading}
        handleSubmitReservationModal={(values) =>
          handleSubmitReservationModal(values)
        }
        handleCloseReservationModal={handleCloseReservationModal}
      />

      {reservation ? (
        <EventContentModal
          open={openJobEventModal}
          onClose={handleCloseJobEventModal}
          handleOpenReservationModalCancel={handleOpenReservationModalCancel}
          jobCancelled={confirmJobCancelled}
          reservation={reservation}
          onEditReservationClick={openEditReservationModal}
          onNavigateToReservationDetailsClick={
            handleNavigateToReservationDetails
          }
        />
      ) : null}

      <ReservationCancelWithCommentModal
        onSubmit={handleCancelReservation}
        isLoading={isLoading}
        flow={reservationCancelValues.flow}
        cancelationFee={reservationCancelValues.cancelationFee}
        reservationTitle={reservationCancelValues.reservationTitle}
        handleCloseReservationModalCancel={handleCloseReservationModalCancel}
      />

      <ReservationEditModal
        initialFormValues={reservationEditValues}
        handleEditReservation={handleEditReservation}
        isLoading={isLoading}
        fullUpdate
        flow={reservationEditValues.flow}
        handleCloseReservationEditModal={handleCloseReservationEditModal}
      />

      <ConfirmReservationModal
        isOpen={isConfirmModalOpen}
        handleClose={handleCloseConfirmModal}
        handleConfirm={handleConfirmReservation}
      />
    </>
  );
};
