import { useState, useEffect } from "react";

import { Company, getVehiclesDto, SearchVehicleType } from "../types";

export interface VehicleFilterState {
  filters: getVehiclesDto;
  selectedFavoritedCompanies: Company[];
  selectedContractedCompanies: Company[];
  selectedPartneredCompanies: Company[];
  searchType: SearchVehicleType;
}

interface VehicleFiltersDefaultState extends VehicleFilterState {}

export function usePersistentVehicleState(
  key: string,
  searchType: SearchVehicleType
) {
  const pathKey = `vehicle${key}filters`;

  const defaultMasterState: VehicleFiltersDefaultState = {
    filters: {
      type: "Pump",
    } as getVehiclesDto,
    selectedFavoritedCompanies: [],
    selectedContractedCompanies: [],
    selectedPartneredCompanies: [],
    searchType,
  };

  const getInitialState = (): VehicleFiltersDefaultState => {
    try {
      const storedValue = localStorage.getItem(pathKey);
      if (!storedValue) return defaultMasterState;

      const parsed: VehicleFiltersDefaultState = JSON.parse(storedValue);

      return {
        ...parsed,
        filters: {
          ...parsed.filters,
          dateFrom: parsed.filters.dateFrom
            ? new Date(parsed.filters.dateFrom)
            : null,
          dateTo: parsed.filters.dateTo
            ? new Date(parsed.filters.dateTo)
            : null,
        },
      };
    } catch (error) {
      console.error("Error reading from localStorage:", error);
      return defaultMasterState;
    }
  };
  const initialState = getInitialState();
  const [queryFilterState, setQueryFilterState] =
    useState<VehicleFiltersDefaultState>(initialState);

  useEffect(() => {
    try {
      localStorage.setItem(pathKey, JSON.stringify(queryFilterState));
    } catch (error) {
      console.error("Error writing to localStorage:", error);
    }
  }, [pathKey, queryFilterState]);

  return [queryFilterState, setQueryFilterState] as const;
}
