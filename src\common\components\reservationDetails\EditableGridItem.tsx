import React from "react";
import { Grid, Stack, Typography } from "@mui/material";
import { EditableFields } from "src/common/types";

interface EditableGridItemProps {
  field: EditableFields;
  value: number | boolean;
  displayValue: string;
  isEditable?: boolean;
  editComponent: React.ReactNode;
  align?: "left" | "right";
  xs?: number;
  sm?: number;
  isEditing: boolean;
}

const EditableGridItem: React.FC<EditableGridItemProps> = ({
  field,
  value,
  displayValue,
  isEditable = true,
  editComponent,
  align = "right",
  xs = 6,
  sm = 2,
  isEditing,
}) => {
  return (
    <Grid item xs={xs} sm={sm}>
      <Stack
        direction="row"
        spacing={1}
        justifyContent={align === "right" ? "flex-end" : "flex-start"}
        alignItems="center"
      >
        {isEditing ? (
          <>{editComponent}</>
        ) : (
          <Typography variant="body2" align={align}>
            {displayValue}
          </Typography>
        )}
      </Stack>
    </Grid>
  );
};

export default EditableGridItem;
