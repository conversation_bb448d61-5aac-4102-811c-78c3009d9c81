import React, { use<PERSON>allback, useMemo, useEffect, useRef } from "react";
import { Autocomplete, Box, InputAdornment, Stack } from "@mui/material";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import enGb from "date-fns/locale/en-GB";
import { FormikProvider, useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { Calendar04Icon, FilterIcon } from "@hugeicons/react";
import { CeTextField, CeButton, CePaper } from "src/common/components";
import {
  VehicleTypes,
  vehicleTypes,
  SearchVehicleType,
  searchVehicleTypes,
  Company,
} from "src/common/types";
import { PlanningFilterValues } from "src/common/types/planning";
import {
  usePersistentVehicleState,
  VehicleFilterState,
} from "src/common/utils/vehicleFilterState";
import { useLocation } from "react-router-dom";


interface PlanningFiltersProps {
  filters: PlanningFilterValues;
  onFiltersChange: (filters: PlanningFilterValues) => void;
  onClearFilters: () => void;
  filterOptions: {
    operators: string[];
    types: string[];
    customers: string[];
    siteAddresses: string[];
    boomSizes: number[];
    companies: Company[];
  };
  isLoading?: boolean;
  hideFields?: string[];
  useVehicleState?: boolean;
  localStorageKey?: string;
  defaultSearchType?: SearchVehicleType;
  onVehicleStateChange?: (state: VehicleFilterState) => void;
  allFavorites?: { data: any[] };
  allContracted?: { data: any[] };
  partneredCompanies?: Company[];
  calendarRef?: React.RefObject<any>;
}

export const PlanningFilters: React.FC<PlanningFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  filterOptions,
  isLoading = false,
  hideFields = [],
  useVehicleState = false,
  localStorageKey,
  defaultSearchType = "Partnered",
  onVehicleStateChange,
  allFavorites,
  allContracted,
  partneredCompanies = [],
  calendarRef,
}) => {
  const { t } = useTranslation(["common", "manager", "dispatcher"]);

  const boomSizeRef = useRef<HTMLInputElement>(null);
  const operatorRef = useRef<HTMLInputElement>(null);
  const customerRef = useRef<HTMLInputElement>(null);
  const siteAddressRef = useRef<HTMLInputElement>(null);

  const [queryFilterState, setQueryFilterState] = usePersistentVehicleState(
    localStorageKey || "fallback",
    defaultSearchType
  );

  const {
    searchType,
    selectedContractedCompanies,
    selectedFavoritedCompanies,
    selectedPartneredCompanies,
  } = useVehicleState
    ? queryFilterState
    : {
        searchType: undefined,
        selectedContractedCompanies: [],
        selectedFavoritedCompanies: [],
        selectedPartneredCompanies: [],
      };

  const getCurrentCompanies = useCallback((): Company[] | null => {
    if (!useVehicleState || !searchType) return null;

    switch (searchType) {
      case "Favorited":
        return selectedFavoritedCompanies || [];
      case "Contracted":
        return selectedContractedCompanies || [];
      case "Partnered":
        return selectedPartneredCompanies || [];
      default:
        return null;
    }
  }, [
    useVehicleState,
    searchType,
    selectedFavoritedCompanies,
    selectedContractedCompanies,
    selectedPartneredCompanies,
  ]);

  const formik = useFormik<PlanningFilterValues>({
    initialValues: {
      ...filters,
      searchCategory: useVehicleState
        ? searchType || defaultSearchType
        : filters.searchCategory,
      companies: useVehicleState ? getCurrentCompanies() : filters.companies,
    },
    enableReinitialize: true,
    validationSchema: yup.object({
      dateFrom: yup
        .date()
        .nullable()
        .test("dateFrom", t("common:date-from-error")!, function (value) {
          if (!value) return true;
          const { dateTo } = this.parent;
          return !dateTo || value < dateTo;
        }),
      dateTo: yup
        .date()
        .nullable()
        .test("dateTo", t("common:date-to-error")!, function (value) {
          if (!value) return true;
          const { dateFrom } = this.parent;
          return !dateFrom || value > dateFrom;
        }),
      operator: yup.string().nullable(),
      type: yup.string().nullable(),
      siteAddress: yup.string().nullable(),
      customer: yup.string().nullable(),
      boomSize: yup.number().nullable(),
      searchCategory: yup.string().nullable(),
      companies: yup.array().nullable(),
    }),
    onSubmit: (values) => {
      onFiltersChange(values);
    },
  });

  useEffect(() => {
    if (!useVehicleState) return;

    const favorites = allFavorites?.data || [];
    const companies = favorites.map((fav: any) => fav.operatorCompany) || [];

    if (
      searchType === "Favorited" &&
      selectedFavoritedCompanies?.length === 0 &&
      companies.length
    ) {
      setQueryFilterState((state: VehicleFilterState) => ({
        ...state,
        selectedFavoritedCompanies: companies,
      }));
    }
  }, [
    searchType,
    selectedFavoritedCompanies,
    allFavorites?.data,
    useVehicleState,
  ]);

  useEffect(() => {
    if (!useVehicleState) return;

    const contracts = allContracted?.data || [];
    const contractCompanies =
      contracts.map((contract: any) => contract.operatorCompany) || [];

    if (
      searchType === "Contracted" &&
      selectedContractedCompanies?.length === 0 &&
      contractCompanies.length
    ) {
      setQueryFilterState((state: VehicleFilterState) => ({
        ...state,
        selectedContractedCompanies: contractCompanies,
      }));
    }
  }, [
    searchType,
    selectedContractedCompanies,
    allContracted?.data,
    useVehicleState,
  ]);

  useEffect(() => {
    if (!useVehicleState) return;

    if (
      searchType === "Partnered" &&
      selectedPartneredCompanies?.length === 0 &&
      partneredCompanies.length
    ) {
      setQueryFilterState((state: VehicleFilterState) => ({
        ...state,
        selectedPartneredCompanies: partneredCompanies,
      }));
    }
  }, [
    searchType,
    selectedPartneredCompanies,
    partneredCompanies,
    useVehicleState,
  ]);

  useEffect(() => {
    if (useVehicleState && queryFilterState && onVehicleStateChange) {
      onVehicleStateChange(queryFilterState);
    }
  }, [queryFilterState, onVehicleStateChange, useVehicleState]);

  const handleFilterChange = useCallback(
    (field: keyof PlanningFilterValues, value: any) => {
      formik.setFieldValue(field, value);

      if (useVehicleState) {
        if (field === "searchCategory") {
          setQueryFilterState((state: VehicleFilterState) => ({
            ...state,
            searchType: value,
          }));
        } else if (field === "companies" && searchType) {
          switch (searchType) {
            case "Favorited":
              setQueryFilterState((state: VehicleFilterState) => ({
                ...state,
                selectedFavoritedCompanies: value,
              }));
              break;
            case "Contracted":
              setQueryFilterState((state: VehicleFilterState) => ({
                ...state,
                selectedContractedCompanies: value,
              }));
              break;
            case "Partnered":
              setQueryFilterState((state: VehicleFilterState) => ({
                ...state,
                selectedPartneredCompanies: value,
              }));
              break;
          }
        }
      }
    },
    [formik, useVehicleState, setQueryFilterState, searchType]
  );

  const captureCurrentInputValues = useCallback(() => {
    const formElement = document.getElementById("planning-filters-form");
    if (!formElement) return;

    const inputs = formElement.querySelectorAll("input");
    inputs.forEach((input) => {
      const fieldId = input.id || input.getAttribute("data-field");
      if (fieldId && input.value !== undefined) {
        switch (fieldId) {
          case "boomSize":
            const boomValue = input.value
              ? parseFloat(input.value) || null
              : null;
            if (boomValue !== formik.values.boomSize) {
              formik.setFieldValue("boomSize", boomValue);
            }
            break;
          case "operator":
            const operatorValue = input.value || null;
            if (operatorValue !== formik.values.operator) {
              formik.setFieldValue("operator", operatorValue);
            }
            break;
          case "customer":
            const customerValue = input.value || null;
            if (customerValue !== formik.values.customer) {
              formik.setFieldValue("customer", customerValue);
            }
            break;
          case "siteAddress":
            const siteAddressValue = input.value || null;
            if (siteAddressValue !== formik.values.siteAddress) {
              formik.setFieldValue("siteAddress", siteAddressValue);
            }
            break;
        }
      }
    });
  }, [formik]);

  const handleSubmit = useCallback(() => {
    captureCurrentInputValues();
      formik.submitForm();
  }, [captureCurrentInputValues, formik]);

  const handleClearFilters = useCallback(() => {
    formik.resetForm();
    if (boomSizeRef.current) {
      boomSizeRef.current.value = "";
    }
    if (operatorRef.current) {
      operatorRef.current.value = "";
    }
    if (customerRef.current) {
      customerRef.current.value = "";
    }
    if (siteAddressRef.current) {
      siteAddressRef.current.value = "";
    }

    if (calendarRef?.current) {
      const calendarApi = calendarRef.current.getApi();
      const currentView = calendarApi.view.type;

      if (currentView !== "resourceTimelineWeek") {
        calendarApi.changeView("resourceTimelineWeek");
      }
    }

    onClearFilters();
  }, [formik, onClearFilters, calendarRef]);

  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(
      (value) => value !== null && value !== ""
    );
  }, [filters]);

  const getCompaniesOptions = useMemo(() => {
    if (!useVehicleState) {
      return filterOptions.companies;
    }

    switch (searchType) {
      case "Favorited":
        return (allFavorites?.data || []).map(
          (fav: any) => fav.operatorCompany
        );
      case "Contracted":
        return (allContracted?.data || []).map(
          (contract: any) => contract.operatorCompany
        );
      case "Partnered":
        return partneredCompanies;
      default:
        return filterOptions.companies;
    }
  }, [
    useVehicleState,
    searchType,
    allFavorites?.data,
    allContracted?.data,
    partneredCompanies,
    filterOptions.companies,
  ]);

  return (
    <CePaper sx={{ p: 2, mb: 2 }}>
      <FormikProvider value={formik}>
        <form id="planning-filters-form">
          <Stack spacing={2}>
            <Stack
              direction="row"
              spacing={2}
              alignItems="center"
              flexWrap="wrap"
              justifyContent="space-between"
            >
              <Stack
                direction="row"
                spacing={2}
                alignItems="center"
                flexWrap="wrap"
              >
                {/* Search Category Filter - Dispatcher Only */}
                {!hideFields.includes("searchCategory") && (
                  <Autocomplete
                    id="searchCategory"
                    value={formik.values.searchCategory}
                    onChange={(event, newValue) =>
                      handleFilterChange("searchCategory", newValue)
                    }
                    options={searchVehicleTypes}
                    sx={{ minWidth: 160 }}
                    size="small"
                    disabled={isLoading}
                    renderInput={(params) => (
                      <CeTextField
                        {...params}
                        label={t("common:search-category")}
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                )}

                {/* Companies Filter - Dispatcher Only */}
                {!hideFields.includes("companies") && (
                  <Autocomplete
                    multiple
                    id="companies"
                    value={formik.values.companies || []}
                    onChange={(event, newValue) =>
                      handleFilterChange("companies", newValue)
                    }
                    options={getCompaniesOptions}
                    getOptionLabel={(option) => option?.name || ""}
                    sx={{ minWidth: 220 }}
                    size="small"
                    disabled={isLoading || (useVehicleState && !searchType)}
                    limitTags={2}
                    renderInput={(params) => (
                      <CeTextField
                        {...params}
                        label={t("common:companies")}
                        InputLabelProps={{ shrink: true }}
                        placeholder={
                          useVehicleState && !searchType
                            ? "Select search category first"
                            : t("common:search-companies")
                        }
                      />
                    )}
                  />
                )}

                {/* Date Range Filters */}
                <LocalizationProvider
                  dateAdapter={AdapterDateFns}
                  adapterLocale={enGb}
                >
                  <DateTimePicker
                    label={t("common:date-from")}
                    value={formik.values.dateFrom}
                    onChange={(newValue) =>
                      handleFilterChange("dateFrom", newValue)
                    }
                    renderInput={(params: any) => (
                      <CeTextField
                        {...params}
                        size="small"
                        sx={{ minWidth: 150 }}
                        error={
                          formik.touched.dateFrom &&
                          Boolean(formik.errors.dateFrom)
                        }
                        helperText={
                          formik.touched.dateFrom && formik.errors.dateFrom
                        }
                      />
                    )}
                    disabled={isLoading}
                  />
                </LocalizationProvider>

                <LocalizationProvider
                  dateAdapter={AdapterDateFns}
                  adapterLocale={enGb}
                >
                  <DateTimePicker
                    label={t("common:date-to")}
                    value={formik.values.dateTo}
                    onChange={(newValue) =>
                      handleFilterChange("dateTo", newValue)
                    }
                    renderInput={(params: any) => (
                      <CeTextField
                        {...params}
                        size="small"
                        sx={{ minWidth: 150 }}
                        error={
                          formik.touched.dateTo && Boolean(formik.errors.dateTo)
                        }
                        helperText={
                          formik.touched.dateTo && formik.errors.dateTo
                        }
                      />
                    )}
                    disabled={isLoading}
                  />
                </LocalizationProvider>
              </Stack>
            </Stack>

            <Stack
              direction="row"
              spacing={2}
              alignItems="center"
              flexWrap="wrap"
              justifyContent="space-between"
            >
              <Stack
                direction="row"
                spacing={2}
                alignItems="center"
                flexWrap="wrap"
              >
                {/* Type Filter */}
                {!hideFields.includes("type") && (
                  <Autocomplete
                    id="type"
                    value={formik.values.type}
                    onChange={(event, newValue) =>
                      handleFilterChange("type", newValue)
                    }
                    options={vehicleTypes}
                    sx={{ minWidth: 120 }}
                    size="small"
                    disabled={isLoading}
                    renderInput={(params) => (
                      <CeTextField
                        {...params}
                        label={t("common:type")}
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                )}

                {/* BoomSize Filter */}
                <Autocomplete
                  id="boomSize"
                  value={formik.values.boomSize}
                  onChange={(event, newValue) => {
                    const numericValue =
                      typeof newValue === "string"
                        ? parseFloat(newValue) || null
                        : newValue;
                    handleFilterChange("boomSize", numericValue);
                  }}
                  options={filterOptions.boomSizes}
                  getOptionLabel={(option) => option?.toString() || ""}
                  sx={{ minWidth: 140 }}
                  size="small"
                  freeSolo={true}
                  disabled={isLoading}
                  renderInput={(params) => (
                    <CeTextField
                      {...params}
                      inputRef={boomSizeRef}
                      label={t("common:vehicle-boom-size")}
                      type="number"
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <InputAdornment position="end">
                            {t("meter-symbol")}
                          </InputAdornment>
                        ),
                      }}
                      InputLabelProps={{ shrink: true }}
                    />
                  )}
                />

                {/* Operator Filter */}
                <Autocomplete
                  id="operator"
                  value={formik.values.operator}
                  onChange={(event, newValue) =>
                    handleFilterChange("operator", newValue)
                  }
                  options={filterOptions.operators}
                  sx={{ minWidth: 160 }}
                  size="small"
                  freeSolo={true}
                  disabled={isLoading}
                  clearOnBlur={false}
                  handleHomeEndKeys
                  renderInput={(params) => (
                    <CeTextField
                      {...params}
                      inputRef={operatorRef}
                      label={t("common:operator")}
                      InputLabelProps={{ shrink: true }}
                    />
                  )}
                />

                {/* Customer Filter */}
                <Autocomplete
                  id="customer"
                  value={formik.values.customer}
                  onChange={(event, newValue) =>
                    handleFilterChange("customer", newValue)
                  }
                  options={filterOptions.customers}
                  sx={{ minWidth: 160 }}
                  size="small"
                  disabled={isLoading}
                  freeSolo={true}
                  renderInput={(params) => (
                    <CeTextField
                      {...params}
                      inputRef={customerRef}
                      label={t("common:client")}
                      InputLabelProps={{ shrink: true }}
                    />
                  )}
                />

                {/* Site Address Filter */}
                {!hideFields.includes("siteAddress") && (
                  <Autocomplete
                    id="siteAddress"
                    value={formik.values.siteAddress}
                    onChange={(event, newValue) =>
                      handleFilterChange("siteAddress", newValue)
                    }
                    options={filterOptions.siteAddresses}
                    sx={{ minWidth: 180 }}
                    freeSolo={true}
                    size="small"
                    disabled={isLoading}
                    renderInput={(params) => (
                      <CeTextField
                        {...params}
                        inputRef={siteAddressRef}
                        label={t("common:site-address")}
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                )}
              </Stack>
              {/* Action Buttons */}
              <Stack direction="row" spacing={1}>
                <CeButton
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={isLoading}
                  size="small"
                  startIcon={<FilterIcon size={16} />}
                >
                  {t("common:search")}
                </CeButton>

                {hasActiveFilters && (
                  <CeButton
                    variant="outlined"
                    onClick={handleClearFilters}
                    disabled={isLoading}
                    size="small"
                  >
                    {t("common:clear")}
                  </CeButton>
                )}
              </Stack>
            </Stack>
          </Stack>
        </form>
      </FormikProvider>
    </CePaper>
  );
};
