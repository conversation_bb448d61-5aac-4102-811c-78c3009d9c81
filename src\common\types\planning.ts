import { VehicleTypes, SearchVehicleType, Company } from "./index";

export interface PlanningFilterValues {
  dateFrom: Date | null;
  dateTo: Date | null;
  operator: string | null;
  type: VehicleTypes | null;
  siteAddress: string | null;
  customer: string | null;
  boomSize: number | null;
  searchCategory: SearchVehicleType | null;
  companies: Company[] | null;
}

export interface PlanningFilterOptions {
  operators: string[];
  types: string[];
  customers: string[];
  siteAddresses: string[];
  boomSizes: number[];
  companies: Company[];
}