import { useMemo } from "react";
import { PlanningFilterValues } from "src/common/types/planning";
import { VehicleReservation } from "src/common/types";

export interface FilteredVehiclesOptions {
  vehiclesWithReservations: VehicleReservation[];
  planningFilters: PlanningFilterValues;
  additionalFilters?: {
    [key: string]: any;
  };
}

export const useFilteredVehicles = ({
  vehiclesWithReservations,
  planningFilters,
  additionalFilters = {},
}: FilteredVehiclesOptions): VehicleReservation[] => {
  return useMemo(() => {
    if (!vehiclesWithReservations.length) return [];

    try {
      return vehiclesWithReservations
        .filter((vehicle) => {
          if (
            planningFilters.operator &&
            vehicle.operatorName !== planningFilters.operator
          )
            return false;
          
          if (planningFilters.type && vehicle.type !== planningFilters.type)
            return false;
          
          if (additionalFilters.type && vehicle.type !== additionalFilters.type) 
            return false;
          
          if (additionalFilters.boomSize && vehicle.boomSize !== additionalFilters.boomSize)
            return false;
          
          return true;
        })
        .map((vehicle) => ({
          ...vehicle,
          reservations: vehicle.reservations.filter((reservation) => {
            try {
              if (planningFilters.dateFrom && reservation.dateFrom) {
                const reservationDate = new Date(reservation.dateFrom);
                const filterFromDate = new Date(planningFilters.dateFrom);
                if (reservationDate < filterFromDate) return false;
              }
              
              if (planningFilters.dateTo && reservation.dateTo) {
                const reservationDate = new Date(reservation.dateTo);
                const filterToDate = new Date(planningFilters.dateTo);
                if (reservationDate > filterToDate) return false;
              }
              
              if (additionalFilters.dateFrom && reservation.dateFrom) {
                const reservationDate = new Date(reservation.dateFrom);
                const filterFromDate = new Date(additionalFilters.dateFrom);
                if (reservationDate < filterFromDate) return false;
              }
              
              if (additionalFilters.dateTo && reservation.dateTo) {
                const reservationDate = new Date(reservation.dateTo);
                const filterToDate = new Date(additionalFilters.dateTo);
                if (reservationDate > filterToDate) return false;
              }
              
              if (
                planningFilters.customer &&
                vehicle.companyName !== planningFilters.customer
              )
                return false;

              if (
                planningFilters.siteAddress &&
                reservation.siteAddress !== planningFilters.siteAddress
              )
                return false;
              
              if (
                additionalFilters.siteAddress &&
                reservation.siteAddress !== additionalFilters.siteAddress
              )
                return false;
              
              return true;
            } catch (error) {
              console.error("Error filtering reservation:", error);
              return false;
            }
          }),
        }))
        .filter((vehicle) => {
          const hasActivePlanningFilters = Object.entries(planningFilters).some(
            ([key, value]) => {
              return value !== null && value !== "";
            }
          );
          
          const hasActiveAdditionalFilters = Object.entries(additionalFilters).some(
            ([key, value]) => {
              if (key === "coordinates" || key === "radius") return false;
              return value !== null && value !== "";
            }
          );
          
          return (
            !(hasActivePlanningFilters || hasActiveAdditionalFilters) ||
            vehicle.reservations.length > 0
          );
        });
    } catch (error) {
      console.error("Error filtering vehicles:", error);
      return vehiclesWithReservations;
    }
  }, [vehiclesWithReservations, planningFilters, additionalFilters]);
};