import { User } from "./user";
import { Vehicle } from "./vehicle";
import { CommonEntity, Location, PossibleSortDir } from "./common";
import { CreateJobDto, Job, JobFormValues, UpdateJobDto } from "./job";
import { ContainerPricelists, PriceList } from "./priceList";
import { GridSortModel } from "@mui/x-data-grid";
import { Expression } from "./filters";
import { Operator } from "./operator";

export interface Reservation extends CommonEntity {
  id: number;
  orderNumber: string | null;
  dateFrom: string | null;
  dateTo: string | null;
  siteAddress: string | null;
  city: string | null;
  plz: number | null;
  location: Location | null;
  clientDetails: ClientDetails | null;
  reservationType: ReservationType | null;
  manager: User | null;
  operator: Operator | null;
  dispatcher?: User | null;
  managerId: number | null;
  operatorId: number | null;
  vehicleId: number | null;
  dispatcherId: number | null;
  vehicle: Vehicle | null;
  job: Job | null;
  localAdministrationAuthorizationKey?: string;
  trafficPlanKey?: string;
  parkingPermitAcquiredKey?: string;
  invoiced: boolean;
  invoiceNumber: string | null;
  totalSum: number | null;
  pricelistId: number | null;
  pricelist: PriceList | null;
  hasBeenRead: boolean;
  vehicleUniqueId?: string;
}

export interface ClientDetails {
  name: string | null;
  lastName: string | null;
  email: string | null;
  phoneNumber: string | null;
  companyName: string | null;
  companyVatNumber: string | null;
}

export interface ReservationFullFormValues {
  orderNumber?: string;
  reservationId?: number | null;
  manager?: User | null;
  dispatcher?: User | null;
  operator?: Operator | null;
  vehicle?: Vehicle | null;
  reservationType?: string | null;
  dateFrom?: string | null;
  dateTo?: string | null;
  siteAddress?: string | null;
  location?: Location;
  clientDetails?: ClientDetails;
  city: string | null;
  plz: number | null;
  localAdministrationAuthorizationFile?: File | null;
  trafficPlanFile?: File | null;
  parkingPermitAcquiredFile?: File | null;
  localAdministrationAuthorizationKey?: string;
  trafficPlanKey?: string;
  parkingPermitAcquiredKey?: string;
  job?: JobFormValues;
  jobId?: number;
  pricelist?: PriceList | null;
  pricelistContainer?: ContainerPricelists | null;
  hasBeenRead?: boolean;
  siteDistance?: number;
}

export interface ReservationFormValues {
  reservationId?: number | null;
  vehicle?: Vehicle | null;
  dateFrom?: string | null;
  dateTo?: string | null;
  siteAddress?: string | null;
  location?: Location;
  flow?: ReservationModalFlow;
  invoiced?: boolean;
  city?: string | null;
  plz?: number | null;
  hasAssignedPricelist?: boolean;
}

export interface CreateReservationDto {
  orderNumber?: string;
  managerId: number;
  dispatcherId?: number;
  vehicleId: number;
  operatorId: number;
  reservationType: ReservationType;
  dateFrom: string;
  dateTo: string;
  siteAddress?: string | null;
  city?: string | null;
  plz?: number | null;
  location?: Location | null;
  clientDetails: ClientDetails;
  job: CreateJobDto;
  localAdministrationAuthorizationFile?: File | null;
  trafficPlanFile?: File | null;
  parkingPermitAcquiredFile?: File | null;
  pricelist?: PriceList | null;
}

export interface UpdateReservationDto
  extends Omit<Partial<CreateReservationDto>, "job"> {
  jobId?: number;
  reservationId: number;
  hasBeenRead?: boolean;
  pricelistId?: number;
  job?: UpdateJobDto;
}

export interface DeleteReservationModalValues {
  reservationId?: number;
  reservationTitle?: string;
  flow: ReservationModalDeleteFlow;
}

export interface DeleteReservationDto {
  reservationId: number;
}

export interface CancelReservationModalValues {
  reservationId: number | null;
  jobId: number | null;
  reservationTitle: string | null;
  cancelationFee: number | null;
  flow: ReservationModalCancelFlow;
}

export interface ReservationWithCount {
  totalCount: number;
  data: Reservation[];
}

export interface getReservationDto {
  sortModel?: GridSortModel;
  expressions?: Expression[];
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDir?: PossibleSortDir;
  searchText?: string;
  dateFrom?: string;
  dateTo?: string;
  vehicleIds?: number[] | null;
  operatorIds?: number[] | null;
  managerIds?: number[] | null;
  dispatcherIds?: number[] | null;
  dispatcherCompanyId?: number | null;
}

export interface ReservationQueryFilters {
  dateFrom: string | null;
  dateTo: string | null;
  siteAddress: string | null;
  vehicleId: string | null;
  dispatcherId?: string | null;
  editMode?: boolean;
  city?: string | null;
  plz?: number | null;
  lat?: number | null;
  lng?: number | null;
  searchType?: string | null;
}
export interface ReservationEvent {
  id: string;
  resourceId: string;
  title: string;
  start: string;
  end: string;
  extendedProps: {
    schedule: string;
    from: string;
    scheduleFrom: string;
    scheduleTo: string;
    address: string | null;
    clientName: string | null;
    quantity: number;
    dispatcherId: number;
    managerPhoneNumber: string;
    clientPhoneNumber: string;
    extraCementBags: boolean;
    units: number;
    rigidPipeLength100Mm: number | null;
    rigidPipeLength120Mm: number | null;
    flexiblePipeLength80Mm: number;
    flexiblePipeLength90Mm: number;
    comments: string;
    type: string;
    jobStatus: string;
  };
}
export interface DispatcherReservationEvent {
  id: string;
  resourceId: string;
  title?: string;
  start: string;
  end: string;
  display?: string;
  backgroundColor?: string;
  extendedProps?: {
    schedule: string;
    address: string;
    quantity: number;
    flowRate: number;
    scheduleFrom: string;
    scheduleTo: string;
    type: string;
    jobStatus: string;
    ownReservation: boolean;
    hasBeenRead: boolean;
    city: string;
    pricelistId: number;
  };
}

export interface EditReservationDurationModalValues {
  flow: ReservationEditTimeDurationFlow;
}
export interface ManagerReservationEvent {
  id: string;
  resourceId: string;
  title?: string;
  start: string;
  end: string;
  display?: string;
  backgroundColor?: string;
  extendedProps?: {
    schedule: string;
    address: string;
    city: string;
    quantity: number;
    flowRate: number;
    scheduleFrom: string;
    scheduleTo: string;
    type: string;
    jobStatus: string;
    ownReservation: boolean;
    hasBeenRead: boolean;
  };
}

export interface ReportFields {
  amountOfConcrete: number;
  flexiblePipeLength80Mm: number;
  flexiblePipeLength90Mm: number;
  rigidPipeLength100Mm: number;
  rigidPipeLength120Mm: number;
  extraCementBags: boolean;
  cementBags: number;
  secondTechnician: boolean;
};

export interface JobFields {
  cleaning: "Central" | null;
  barbotine: boolean;
  supplyOfTheChemicalSlushie: boolean;
  units: number;
};


export interface UpdateReservationReportDto {
  jobId: number;
  amountOfConcrete: number;
  flexiblePipeLength80Mm: number;
  flexiblePipeLength90Mm: number;
  rigidPipeLength100Mm: number;
  rigidPipeLength120Mm: number;
  extraCementBags: boolean;
  cementBags: number;
  secondTechnician: boolean;
}

export interface UpdateReservationJobDto {
  jobId: number;
  reservationId?: number;
  cleaning: "Central" | null;
  barbotine: boolean;
  supplyOfTheChemicalSlushie: boolean;
  units: number;
}

export type ReservationModalFlow = "Add" | "Edit" | null;
export type ReservationModalDeleteFlow = "Delete" | null;
export type ReservationModalCancelFlow = "Cancel" | null;
export type ReservationEditTimeDurationFlow = "Edit Time" | null;
export type ReservationType = "RESERVATION" | "UNAVAILABLE";
export type EditedValues = ReportFields & JobFields;
export type EditableFields = keyof EditedValues;
