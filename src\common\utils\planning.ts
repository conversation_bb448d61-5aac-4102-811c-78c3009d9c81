import { PlanningFilterOptions } from "../types/planning";

export const generatePlanningFilterOptions = (
  vehiclesWithReservations: any[],
  searchType?: string,
  allFavorites?: { data: any[] },
  allContracted?: { data: any[] },
  partneredCompanies?: any[]
): PlanningFilterOptions => {
  const operators = new Set<string>();
  const types = new Set<string>();
  const customers = new Set<string>();
  const siteAddresses = new Set<string>();
  const boomSizes = new Set<number>();

  vehiclesWithReservations.forEach((vehicle) => {
    if (vehicle.operatorName?.trim()) {
      operators.add(vehicle.operatorName.trim());
    }
    if (vehicle.type?.trim()) {
      types.add(vehicle.type.trim());
    }
    if (vehicle.boomSize) {
      boomSizes.add(vehicle.boomSize);
    }
    if (Array.isArray(vehicle.reservations)) {
      vehicle.reservations.forEach((reservation: any) => {
        if (
          reservation.clientDetails?.name &&
          reservation.clientDetails?.lastName
        ) {
          const fullName =
            `${reservation.clientDetails.name} ${reservation.clientDetails.lastName}`.trim();
          if (fullName) {
            customers.add(fullName);
          }
        }
        if (reservation.siteAddress?.trim()) {
          siteAddresses.add(reservation.siteAddress.trim());
        }
      });
    }
  });

  let companies: any[] = [];
  switch (searchType) {
    case "Favorited":
      companies = (allFavorites?.data || []).map((fav) => fav.operatorCompany);
      break;
    case "Contracted":
      companies = (allContracted?.data || []).map(
        (contract) => contract.operatorCompany
      );
      break;
    case "Partnered":
      companies = partneredCompanies || [];
      break;
    default:
      companies = [];
      break;
  }

  return {
    operators: Array.from(operators).sort((a, b) => a.localeCompare(b)),
    types: Array.from(types).sort((a, b) => a.localeCompare(b)),
    customers: Array.from(customers).sort((a, b) => a.localeCompare(b)),
    siteAddresses: Array.from(siteAddresses).sort((a, b) => a.localeCompare(b)),
    boomSizes: Array.from(boomSizes).sort((a, b) => a - b),
    companies: companies,
  };
};
