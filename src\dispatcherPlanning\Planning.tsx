import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import deLocale from "@fullcalendar/core/locales/de";
import enLocale from "@fullcalendar/core/locales/en-gb";
import frLocale from "@fullcalendar/core/locales/fr";
import nlLocale from "@fullcalendar/core/locales/nl";
import {
  DatesSetArg,
  EventChangeArg,
  EventClickArg,
  EventContentArg,
  LocaleInput,
} from "@fullcalendar/core";
import { Autocomplete, Stack } from "@mui/material";
import queryString from "query-string";
import { useRecoilState } from "recoil";
import {
  getCurrentUser,
  useCheckVehicleSlotAvailability,
  useGetContracts,
  useGetFavorites,
  useReservation,
  useUpdateReservation,
  useVehiclesWithReservations,
} from "src/common/api";
import { CePaper, CeTextField } from "src/common/components";
import {
  RESERVATION_CANCEL_DEFAULT,
  RESERVATION_EDIT_DEFAULT,
  RESERVATION_FORM_VALUES,
  VEHICLE_FILTER_VALUES_DEFAULT,
} from "src/common/constants";
import {
  reservationCancelValuesState,
  reservationEditValuesState,
  reservationFormValuesState,
  reservationFullFormValuesState,
  themeState,
  vehicleFilterFormValuesState,
  vehicleInformationFlowState,
} from "src/common/state";
import {
  ContractedStatus,
  DispatcherReservationEvent,
  FilterVehiclesDto,
  JobStatus,
  Reservation,
  ReservationFormValues,
  ReservationQueryFilters,
  SearchVehicleType,
  searchVehicleTypes,
  UnavailablePeriod,
  UpdateReservationDto,
  Vehicle,
} from "src/common/types";
import {
  checkVehicleAvailability,
  collectOperatorCompanyIdsAttr,
  determineCancelationFee,
  reservationPayloadIntoFormData,
  transformVehicleWithReservationsIntoResources,
  turnReservationIntoFormValues,
} from "src/common/utils";
import { FilterQuickForm } from "src/search/form/FilterQuickForm";
import FullCalendar from "@fullcalendar/react";
import { useTranslation } from "react-i18next";
import dayGridPlugin from "@fullcalendar/daygrid";
import listPlugin from "@fullcalendar/list";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import interactionPlugin from "@fullcalendar/interaction";
import {
  endOfDay,
  endOfWeek,
  format,
  formatISO,
  parseISO,
  startOfDay,
  startOfWeek,
  sub,
} from "date-fns";
import { zonedTimeToUtc } from "date-fns-tz";
import ResourceLabelContent from "./resource-label-content/ResourceLabelContent";
import { formatCalendarDate } from "src/common/utils/formatDate";
import { calendarLocales } from "src/i18n";
import EventContentDay from "./event-content/EventContentDay";
import EventContentModal from "./event-content/EventContentModal";
import { useUpdateJob } from "src/common/api/job";
import { ReservationCancelWithCommentModal } from "src/reservations/ReservationCancelWithCommentModal";
import { VehicleInformationModal } from "src/vehicles/vehicleInfoModal/VehicleInformationModal";
import toast from "react-hot-toast";
import { EventImpl } from "@fullcalendar/core/internal";
import { ReservationModal } from "src/reservations/ReservationModal";
import { useLocation, useNavigate } from "react-router-dom";
import EventContentList from "./list-view/ListView";
import { ConfirmReservationModal } from "./ConfirmReservationModal";
import { ReservationEditModal } from "src/reservations/ReservationEditModal";
import { usePartners } from "src/common/api/partner";
import { useCompany } from "src/common/api/company";
import { usePersistentVehicleState } from "src/common/utils/vehicleFilterState";
import { PlanningFilters } from "src/planning/PlanningFilters";
import { PlanningFilterValues } from "src/common/types/planning";
import { PLANNING_FILTERS_DEFAULTS } from "src/common/constants/planning";
import { generatePlanningFilterOptions } from "src/common/utils/planning";
import { UnavailablePeriodPlanning } from "src/common/types";

export const DispatcherPlanning = () => {
  const calendarRef = useRef<FullCalendar | null>(null);
  const localeText: { [key: string]: LocaleInput } = {
    en: enLocale,
    fr: frLocale,
    nl: nlLocale,
    de: deLocale,
  };
  const location = useLocation();
  const localStorageKey = location.pathname.slice(1);

  const [queryFilterState, setQueryFilterState] = usePersistentVehicleState(
    localStorageKey,
    "Partnered"
  );
  const {
    searchType,
    selectedContractedCompanies,
    selectedFavoritedCompanies,
    selectedPartneredCompanies,
    filters,
  } = queryFilterState;
  const { siteAddress, ...quickFormFilters } = filters;
  const [theme] = useRecoilState(themeState);
  const [vehicleInformationValues, setVehicleInformationValues] =
    useRecoilState(vehicleInformationFlowState);
  const [vehicleFilterFormValues, setVehicleFilterFormValues] = useRecoilState(
    vehicleFilterFormValuesState
  );
  const [reservationCancelValues, setReservationCancelValues] = useRecoilState(
    reservationCancelValuesState
  );
  const [reservationFullFormValues, setReservationFullFormValues] =
    useRecoilState(reservationFullFormValuesState);
  const [reservationFormValues, setReservationFormValues] = useRecoilState(
    reservationFormValuesState
  );
  const [reservationEditValues, setReservationEditValues] = useRecoilState(
    reservationEditValuesState
  );
  const navigate = useNavigate();
  const isDarkMode = theme === "dark";
  const { t, i18n } = useTranslation(["manager", "common", "dispatcher"]);
  const selectedLanguage = i18n.language;
  const currentUser = getCurrentUser();
  const dispatcherIds = currentUser?.id ? [currentUser.id] : [];

  const [events, setEvents] = useState<DispatcherReservationEvent[]>([]);
  const [selectedReservationId, setSelectedReservationId] = useState<
    number | null
  >(null);

  const [planningFilters, setPlanningFilters] = useState<PlanningFilterValues>(
    PLANNING_FILTERS_DEFAULTS
  );
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [pendingReservationData, setPendingReservationData] =
    useState<UpdateReservationDto>();
  const [openJobEventModal, setOpenJobEventModal] = useState(false);

  const initialStartDate = startOfWeek(new Date(), { weekStartsOn: 1 });
  const initialEndDate = endOfWeek(new Date(), { weekStartsOn: 1 });
  const [dateRange, setDateRange] = useState({
    dateFrom: zonedTimeToUtc(initialStartDate, "UTC").toISOString(),
    dateTo: zonedTimeToUtc(initialEndDate, "UTC").toISOString(),
  });

  const collectedOperatorCompanyIds = collectOperatorCompanyIdsAttr(
    searchType,
    selectedContractedCompanies,
    selectedFavoritedCompanies,
    selectedPartneredCompanies
  );

  const {
    data: allVehiclesWithReservations,
    isLoading: isFetchingVehiclesWithReservations,
    isSuccess: isVehiclesWithReservationsSuccess,
  } = useVehiclesWithReservations(
    {
      ...quickFormFilters,
      sortBy: "vehicle.boomSize",
      sortDir: "ASC",
      operatorCompanyIds: collectedOperatorCompanyIds,
      searchType: searchType,
      dateFrom: planningFilters.dateFrom
        ? new Date(planningFilters.dateFrom).toISOString()
        : dateRange.dateFrom,
      dateTo: planningFilters.dateTo
        ? new Date(planningFilters.dateTo).toISOString()
        : dateRange.dateTo,
      boomSize: planningFilters.boomSize,
      type: planningFilters.type,
      siteAddress: planningFilters.siteAddress,
      operatorName: planningFilters.operator || undefined,
      clientName: planningFilters.customer || undefined,
    },
    Boolean(dispatcherIds.length)
  );

  const { data: allFavorites, isLoading: isFavoritesLoading } = useGetFavorites(
    { dispatcherCompanyId: currentUser?.companyId },
    Boolean(currentUser?.companyId && searchType === "Favorited")
  );

  const { data: allContracted, isLoading: isContractedLoading } =
    useGetContracts(
      {
        expressions: [],
        sortModel: [],
        dispatcherCompanyId: currentUser?.companyId,
        status: ContractedStatus.APPROVED,
        relations: ["operatorCompany"],
      },
      Boolean(currentUser?.companyId && searchType === "Contracted")
    );

  const { data: allPartners, isLoading: isLoadingPartners } = usePartners(
    { expressions: [], sortModel: [] },
    Boolean(currentUser?.companyId && searchType === "Partnered")
  );

  const partners = allPartners?.data || [];
  const partneredCompanies =
    partners.map((partner) => partner.operatorCompany) || [];

  const { data: reservation, isLoading: isReservationLoading } = useReservation(
    selectedReservationId!,
    Boolean(selectedReservationId)
  );

  const {
    data: companyData,
    isLoading: isCompanyLoading,
    isSuccess: isCompanySuccess,
  } = useCompany(
    reservation?.operator?.companyId,
    Boolean(reservation?.operator?.companyId)
  );

  const {
    mutate: handleUpdateJob,
    isLoading: isUpdatingJob,
    isSuccess: isJobUpdated,
  } = useUpdateJob();

  const {
    mutate: handleUpdateReservation,
    isLoading: isUpdateReservationLoading,
  } = useUpdateReservation();
  const {
    mutateAsync: handleCheckVehicleAvailabilityAsync,
    isLoading: isCheckVehicleAvailabilityLoading,
  } = useCheckVehicleSlotAvailability();

  const isLoading =
    isFetchingVehiclesWithReservations ||
    isCheckVehicleAvailabilityLoading ||
    isFavoritesLoading ||
    isContractedLoading ||
    isLoadingPartners ||
    isReservationLoading ||
    isUpdatingJob ||
    isUpdateReservationLoading;

  const vehiclesWithReservations = useMemo(
    () => allVehiclesWithReservations?.data || [],
    [allVehiclesWithReservations]
  );

  const filterOptions = useMemo(() => {
    return generatePlanningFilterOptions(
      vehiclesWithReservations,
      searchType,
      allFavorites,
      allContracted,
      partneredCompanies
    );
  }, [
    vehiclesWithReservations,
    searchType,
    allFavorites?.data,
    allContracted?.data,
    partneredCompanies,
  ]);

  const [firstLoad, setFirstLoad] = useState(true);

  useEffect(() => {
    if (firstLoad && calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();

      if (calendarApi) {
        calendarApi.changeView("resourceTimelineWeek");
        window.dispatchEvent(new Event("resize"));
      }
    }
  }, [firstLoad, calendarRef.current]);

  useEffect(() => {
    if (!searchType) return;

    switch (searchType) {
      case "Favorited": {
        const favorites = allFavorites?.data || [];
        const companies = favorites.map((fav) => fav.operatorCompany) || [];

        if (selectedFavoritedCompanies.length === 0 && companies.length > 0) {
          setQueryFilterState((state) => ({
            ...state,
            selectedFavoritedCompanies: companies,
          }));
        }
        break;
      }
      case "Contracted": {
        const contracts = allContracted?.data || [];
        const contractCompanies =
          contracts.map((contract) => contract.operatorCompany) || [];

        if (
          selectedContractedCompanies.length === 0 &&
          contractCompanies.length > 0
        ) {
          setQueryFilterState((state) => ({
            ...state,
            selectedContractedCompanies: contractCompanies,
          }));
        }
        break;
      }
      case "Partnered": {
        if (
          selectedPartneredCompanies.length === 0 &&
          partneredCompanies.length > 0
        ) {
          setQueryFilterState((state) => ({
            ...state,
            selectedPartneredCompanies: partneredCompanies,
          }));
        }
        break;
      }
    }
  }, [
    searchType,
    selectedFavoritedCompanies,
    selectedContractedCompanies,
    selectedPartneredCompanies,
    allFavorites?.data,
    allContracted?.data,
    partneredCompanies,
  ]);

  const filteredVehiclesWithReservations = vehiclesWithReservations;

  useEffect(() => {
    if (
      filteredVehiclesWithReservations.length &&
      isVehiclesWithReservationsSuccess
    ) {
      const mappedEvents: DispatcherReservationEvent[] =
        filteredVehiclesWithReservations.flatMap((vehicle) =>
          vehicle.reservations.map((reservation) => ({
            id: reservation.id.toString(),
            resourceId: vehicle.id.toString(),
            title:
              reservation.clientDetails &&
              reservation.clientDetails.name &&
              reservation.clientDetails.lastName
                ? `${reservation.clientDetails.name} ${reservation.clientDetails.lastName}`
                : vehicle.companyName,
            start: reservation.dateFrom || "",
            end: reservation.dateTo || "",
            editable: reservation.ownReservation,
            extendedProps: {
              schedule:
                reservation.dateFrom && reservation.dateTo
                  ? `${format(
                      new Date(reservation.dateFrom),
                      "HH:mm"
                    )} - ${format(new Date(reservation.dateTo), "HH:mm")}`
                  : "",
              from: reservation.dateFrom
                ? `${format(new Date(reservation.dateFrom), "HH:mm")}`
                : "",
              scheduleFrom: reservation.dateFrom
                ? `${format(new Date(reservation.dateFrom), "dd/MM - HH:mm")}`
                : "",
              scheduleTo: reservation.dateTo
                ? `${format(new Date(reservation.dateTo), "dd/MM - HH:mm")}`
                : "",
              address: reservation.siteAddress || "",
              quantity: reservation.amount || 0,
              type: "job",
              jobStatus: reservation.jobStatus || "",
              ownReservation: reservation.ownReservation,
              hasBeenRead: reservation.hasBeenRead,
              dispatcherId: reservation.dispatcherId,
              city: reservation.city,
              pricelistId: reservation.pricelistId,
              flowRate: reservation.flowRate || 0,
            },
          }))
        );
      const mappedUnavailableEvents: DispatcherReservationEvent[] =
        filteredVehiclesWithReservations.flatMap((vehicle) =>
          (vehicle.unavailablePeriods || []).map(
            (period: UnavailablePeriodPlanning) => ({
              id: `unavailable-${vehicle.id}-${period.id}`,
              resourceId: vehicle.id.toString(),
              start: formatISO(parseISO(period.from)),
              end: formatISO(endOfDay(parseISO(period.to))),
              display: "background",
              backgroundColor: period.contract
                ? period.contract?.dispatcherCompanyId ===
                  currentUser?.companyId
                  ? "green"
                  : "red"
                : "red",
            })
          )
        );
      setEvents([...mappedEvents, ...mappedUnavailableEvents]);
    }
  }, [filteredVehiclesWithReservations, isVehiclesWithReservationsSuccess]);

  const handleCloseFilterModal = () => {
    if (!isLoading) {
      setVehicleFilterFormValues(VEHICLE_FILTER_VALUES_DEFAULT);
    }
  };

  const handleFilterVehicle = (newFilters: FilterVehiclesDto) => {
    setQueryFilterState((state) => ({
      ...state,
      filters: { ...quickFormFilters, ...newFilters },
    }));
  };

  const handlePlanningFiltersChange = useCallback(
    (newFilters: PlanningFilterValues) => {
      setPlanningFilters(newFilters);
      if ((newFilters.dateFrom || newFilters.dateTo) && calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();
        const currentView = calendarApi.view.type;

        if (currentView !== "listWeek") {
          calendarApi.changeView("listWeek");
        }
      }
    },
    []
  );

  const clearPlanningFilters = useCallback((): void => {
    setPlanningFilters(PLANNING_FILTERS_DEFAULTS);
  }, []);

  const handleVehicleInformationModal = (vehicleId: number) => {
    setVehicleInformationValues({
      vehicleId,
      flow: "Vehicle Details",
    });
  };

  const updateDateRange = (start: Date, end: Date) => {
    const localStart = startOfDay(start);
    const localEnd = endOfDay(end);
    const newDateRange = {
      dateFrom: zonedTimeToUtc(localStart, "UTC").toISOString(),
      dateTo: zonedTimeToUtc(localEnd, "UTC").toISOString(),
    };
    setDateRange(newDateRange);
  };

  const handleDateClick = (args: DatesSetArg) => {
    const { type } = args.view;
    let start, end;
    switch (type) {
      case "resourceTimelineDay":
        start = end = startOfDay(args.start);

        break;

      case "resourceTimelineWeek":
        start = startOfWeek(args.start, { weekStartsOn: 1 });
        end = endOfWeek(args.start, { weekStartsOn: 1 });

        break;

      default:
        start = startOfDay(args.start);
        end = endOfDay(args.end);
    }
    if (start && end && !firstLoad) {
      updateDateRange(start, end);
    }
  };
  const resources = transformVehicleWithReservationsIntoResources(
    filteredVehiclesWithReservations
  );
  useEffect(() => {
    if (isJobUpdated) {
      setReservationCancelValues(RESERVATION_CANCEL_DEFAULT);
    }
  }, [isJobUpdated, setReservationCancelValues]);

  const dayEventContent = (arg: EventContentArg) => {
    if (arg.event.display === "background") {
      return null;
    }
    const argDate = arg.event.start?.toLocaleDateString("en-CA");
    const argEventVehicle = arg.event._def.resourceIds?.[0];
    const eventsArray = events.filter((event) => {
      const eventDate = new Date(event.start).toLocaleDateString("en-CA");
      return eventDate === argDate && event.resourceId === argEventVehicle;
    });
    return <EventContentDay arg={arg} eventsNumber={eventsArray.length} />;
  };

  const listMonthContent = (arg: EventContentArg) => {
    const resource = resources.find(
      (resource) => resource?.id === arg.event?._def?.resourceIds?.[0]
    );
    return (
      <EventContentList
        arg={arg}
        resource={resource || null}
        handleVehicleInformationModal={handleVehicleInformationModal}
      />
    );
  };

  const handleEventClick = (arg: EventClickArg) => {
    const { ownReservation, type } = arg.event.extendedProps;
    if (!ownReservation) {
      return;
    }
    if (type === "job") {
      setSelectedReservationId(Number(arg.event.id));
      setOpenJobEventModal(true);
    }
  };

  const cancelationFee = determineCancelationFee(
    reservation?.dateFrom!,
    companyData?.settings.cancellation?.cancellationWindow || "",
    companyData?.settings.cancellation?.reducedCancellationWindow || "",
    reservation?.pricelist?.cancellationFee || 0,
    reservation?.pricelist?.dayCancellationFee || 0
  );
  const handleOpenReservationModalCancel = () => {
    setReservationCancelValues({
      reservationId: reservation?.id || null,
      jobId: reservation?.job?.id || null,
      reservationTitle: reservation?.orderNumber || "",
      cancelationFee,
      flow: "Cancel",
    });
  };

  const handleCloseJobEventModal = () => {
    setOpenJobEventModal(false);
    window.dispatchEvent(new Event("resize"));
  };

  const confirmJobCancelled = () => {
    return reservation?.job?.status === "CANCELLED" || false;
  };

  const handleCloseReservationModalCancel = () => {
    if (!isLoading) {
      setReservationCancelValues(RESERVATION_CANCEL_DEFAULT);
    }
  };

  const openEditReservationModal = () => {
    setReservationEditValues({
      reservationId: reservation?.id || null,
      flow: "Edit",
      dateFrom: reservation?.dateFrom || "",
      dateTo: reservation?.dateTo || "",
      siteAddress: reservation?.siteAddress || "",
      city: reservation?.city || "",
      plz: reservation?.plz || null,
      vehicle: reservation?.vehicle || null,
    });
  };

  const handleCloseReservationEditModal = () => {
    setReservationEditValues(RESERVATION_EDIT_DEFAULT);
  };

  const handleCancelReservation = (comment: string) => {
    handleUpdateJob({
      status: JobStatus.CANCELLED,
      jobId: Number(reservationCancelValues.jobId),
      reservationId: Number(reservationCancelValues.reservationId),
      comments: comment,
    });
  };

  const handleCloseReservationModal = () => {
    if (!isLoading) {
      setReservationFormValues(RESERVATION_FORM_VALUES);
    }
  };
  const handleNavigateToReservationDetails = () => {
    navigate(`/reservation/${reservation?.id}`);
  };

  const handleEditReservation = (values: ReservationFormValues) => {
    if (reservation) {
      const reservationQuery: ReservationQueryFilters = {
        dateFrom: values.dateFrom
          ? new Date(values.dateFrom).toISOString()
          : null,
        dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : null,
        siteAddress: values.siteAddress || null,
        city: values.city || null,
        plz: values.plz || null,
        lat: values.location?.coordinates[0] || null,
        lng: values.location?.coordinates[1] || null,
        vehicleId: reservation.vehicle?.id.toString() || null,
        editMode: true,
      };

      const queryParams = queryString.stringify(reservationQuery);
      const reservationFormData = turnReservationIntoFormValues(reservation);
      setReservationFullFormValues(reservationFormData);
      navigate(`/reservation?${queryParams}`);
      handleCloseReservationEditModal();
    }
  };

  const handleCloseConfirmModal = () => {
    if (pendingReservationData) {
      const eventToRestore = events.find(
        (event) => event.id === pendingReservationData.reservationId.toString()
      );

      if (eventToRestore) {
        const originalStart = eventToRestore.start;
        const originalEnd = eventToRestore.end;

        clearEventSlot({
          id: eventToRestore.id,
          start: new Date(originalStart),
          end: new Date(originalEnd),
        } as EventImpl);
      }
    }
    setIsConfirmModalOpen(false);
  };

  const handleConfirmReservation = () => {
    if (pendingReservationData) {
      const formData = reservationPayloadIntoFormData(pendingReservationData);
      handleUpdateReservation({
        reservationId: pendingReservationData.reservationId,
        updateReservationArgs: formData,
      });
    }
    handleCloseConfirmModal();
  };

  const clearEventSlot = (event: EventImpl): void => {
    const eventList = [...events];
    const currentEvent = eventList.find((ev) => ev.id === event.id);

    if (currentEvent) {
      const index = eventList.findIndex((ev) => ev.id === currentEvent.id);
      eventList[index] = {
        ...currentEvent,
        start: event.start?.toISOString() || "",
        end: event.end?.toISOString() || "",
      };

      setEvents(eventList);
    }

    return;
  };

  const eventChange = async (droppedColumn: EventChangeArg) => {
    const event = droppedColumn.event;
    const oldEvent = droppedColumn.oldEvent;
    const currentTime = new Date();

    const reservationStatus = event.extendedProps.jobStatus;

    if (reservationStatus !== "NOT_STARTED") {
      return clearEventSlot(oldEvent);
    }

    if (event.start && event.start < currentTime) {
      return clearEventSlot(oldEvent);
    }

    if (event.end && event.end < currentTime) {
      return clearEventSlot(oldEvent);
    }

    const newVehicleResources = event.getResources();
    const newResourcesProps = newVehicleResources.length
      ? newVehicleResources[0]?.extendedProps
      : null;

    const oldVehicleResources = oldEvent.getResources();
    const oldResourcesProps = oldVehicleResources.length
      ? oldVehicleResources[0]?.extendedProps
      : null;

    const newVehicle = vehiclesWithReservations.find(
      (vehicle) => vehicle.id === Number(newResourcesProps?.vehicleId)
    );

    const oldVehicle = vehiclesWithReservations.find(
      (vehicle) => vehicle.id === Number(oldResourcesProps?.vehicleId)
    );

    const isSlotAvailable = await handleCheckVehicleAvailabilityAsync({
      id: newVehicle?.id!,
      currentReservationId: Number(event.id),
      dateFrom: event.start ? new Date(event.start).toISOString() : "",
      dateTo: event.end ? new Date(event.end).toISOString() : "",
    });

    if (!isSlotAvailable) {
      return clearEventSlot(oldEvent);
    }

    const sameVehicle = newVehicle?.id === oldVehicle?.id;
    const isSameSizeVehicle = newVehicle?.boomSize === oldVehicle?.boomSize;
    const isSameTypeVehicle = newVehicle?.type === oldVehicle?.type;

    if (sameVehicle || (isSameSizeVehicle && isSameTypeVehicle)) {
      const dispatcherId = event._def.extendedProps.dispatcherId;
      const pricelistId = event._def.extendedProps.pricelistId;
      setPendingReservationData({
        reservationId: Number(event.id),
        pricelistId: pricelistId,
        dateFrom: event.start ? new Date(event.start).toISOString() : "",
        dateTo: event.end ? new Date(event.end).toISOString() : "",
        vehicleId: newVehicle?.id!,
        operatorId: newResourcesProps?.operator.id,
        managerId: newResourcesProps?.managerId,
        reservationType: "RESERVATION",
        dispatcherId,
      });
      setIsConfirmModalOpen(true);
    } else {
      return clearEventSlot(oldEvent);
    }
  };

  const handleSubmitReservationModal = async (
    values: ReservationFormValues
  ) => {
    const isSlotAvailable = await handleCheckVehicleAvailabilityAsync({
      id: values.vehicle?.id!,
      currentReservationId: Number(values.reservationId),
      dateFrom: values.dateFrom ? new Date(values.dateFrom).toISOString() : "",
      dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : "",
    });
    // if (!isSlotAvailable) {
    //   toast.error(t("common:slot-not-available"));
    //   return;
    // }

    const reservationQuery: ReservationQueryFilters = {
      dateFrom: values.dateFrom
        ? new Date(values.dateFrom).toISOString()
        : null,
      dateTo: values.dateTo ? new Date(values.dateTo).toISOString() : null,
      siteAddress: values.siteAddress || null,
      city: values.city || null,
      plz: values.plz || null,
      lat: values.location?.coordinates[0] || null,
      lng: values.location?.coordinates[1] || null,
      vehicleId: values.vehicle?.id.toString() || null,
      dispatcherId: currentUser?.id.toString() || "",
      searchType,
    };
    const queryParams = queryString.stringify(reservationQuery);

    setReservationFormValues(RESERVATION_FORM_VALUES);

    navigate(`/reservation?${queryParams}`);
  };

  return (
    <>
      <CePaper variant="outlined" sx={{ mb: 2, p: 1.5 }}>
        <PlanningFilters
          filters={planningFilters}
          onFiltersChange={handlePlanningFiltersChange}
          onClearFilters={clearPlanningFilters}
          filterOptions={filterOptions}
          isLoading={isLoading}
          hideFields={[]}
          useVehicleState={true}
          localStorageKey={localStorageKey}
          defaultSearchType="Partnered"
          onVehicleStateChange={(state) => setQueryFilterState(() => state)}
          allFavorites={allFavorites}
          allContracted={allContracted}
          partneredCompanies={partneredCompanies}
          calendarRef={calendarRef}
        />
      </CePaper>

      <CePaper
        className={isDarkMode ? "dark-mode" : ""}
        sx={{
          background: (theme) => theme.palette.background.paper,
          boxShadow: "0px 1px 3px 0px #0000001F",
          p: 2,
          mb: 2,
        }}
      >
        <FullCalendar
          ref={calendarRef}
          selectable={true}
          firstDay={1}
          nowIndicatorDidMount={() => {
            if (firstLoad) {
              setFirstLoad(false);
            }
          }}
          datesSet={handleDateClick}
          eventChange={async (droppedColumn) => eventChange(droppedColumn)}
          select={(selectionInfo) => {
            if (selectionInfo.resource) {
              const vehicleModel = vehiclesWithReservations.find(
                (vehicle) => vehicle.id === Number(selectionInfo.resource?.id)
              );

              const selectionStart = parseISO(selectionInfo.startStr);
              const selectionEnd = parseISO(selectionInfo.endStr);
              const isUnavailable = vehicleModel?.unavailablePeriods?.filter(
                (period) => {
                  const periodFrom = parseISO(period.from);
                  const periodTo = endOfDay(parseISO(period.to));
                  return selectionStart < periodTo && selectionEnd > periodFrom;
                }
              );

              if (!isUnavailable?.length && searchType === "Contracted") {
                toast.error(t("common:vehicleUnavailable"));
                return;
              }
              if (isUnavailable?.length) {
                const hasOperatorUnavailable = isUnavailable.some(
                  (period) => period.operatorId
                );

                const isOwnReservation = isUnavailable.some(
                  (period) =>
                    period.contract.dispatcherCompanyId ===
                    currentUser?.companyId
                );
                if (hasOperatorUnavailable) {
                  toast.error(t("common:operatorUnavailable"));
                  return;
                }

                if (!isOwnReservation || searchType !== "Contracted") {
                  toast.error(t("common:vehicleUnavailable"));
                  return;
                }
              }

              if (
                vehicleModel?.hasAssignedPricelist === false &&
                searchType !== "Contracted"
              ) {
                toast.error(t("common:vehicle-missing-pricelist"));
                return;
              }
              const vehicle = {
                id: vehicleModel?.id,
                name: vehicleModel?.vehicleUniqueId,
                operator: {
                  id: vehicleModel?.operatorId,
                  name: vehicleModel?.operatorName,
                },
              } as Vehicle;

              if (selectionInfo.view.type === "resourceTimelineDay") {
                const startTime = new Date(selectionInfo.startStr);

                const endTime = new Date(selectionInfo.endStr);

                setReservationFormValues({
                  ...reservationFormValues,
                  dateFrom: startTime.toString(),
                  dateTo: endTime.toString(),
                  vehicle,
                  hasAssignedPricelist: vehicleModel?.hasAssignedPricelist,
                  flow: "Add",
                });
              } else {
                const startTime = startOfDay(new Date(selectionInfo.startStr));

                const endDaySub = sub(new Date(selectionInfo.endStr), {
                  days: 1,
                });

                const endTime = endOfDay(endDaySub);

                setReservationFormValues({
                  ...reservationFormValues,
                  dateFrom: startTime.toString(),
                  dateTo: endTime.toString(),
                  vehicle,
                  hasAssignedPricelist: vehicleModel?.hasAssignedPricelist,
                  flow: "Add",
                });
              }
            }
          }}
          aspectRatio={3}
          dayHeaderDidMount={() =>
            setTimeout(() => window.dispatchEvent(new Event("resize")), 500)
          }
          editable={true}
          eventAllow={(_dropInfo, draggedEvent) =>
            draggedEvent?.extendedProps.ownReservation ?? false
          }
          eventClick={handleEventClick}
          eventDidMount={(arg) => {
            if (arg.event.display !== "background") {
              const eventElement = arg.el;
              eventElement.style.marginLeft = "5px";
              eventElement.style.marginRight = "5px";
            }
          }}
          events={(_fetchInfo, successCallback) => {
            const calendarApi = calendarRef.current?.getApi();
            const currentViewType = calendarApi?.view?.type;
            if (currentViewType === "listMonth") {
              const filteredEvents = events.filter((event) => {
                return event.extendedProps?.ownReservation;
              });
              successCallback(filteredEvents);
            } else {
              successCallback(events);
            }
          }}
          headerToolbar={{
            left: "prev,today,next",
            center: "title",
            right:
              "resourceTimelineDay,resourceTimelineWeek,resourceTimelineMonth,listMonth",
          }}
          height="auto"
          initialView="resourceTimelineDay"
          locale={localeText[selectedLanguage]}
          noEventsDidMount={() => window.dispatchEvent(new Event("resize"))}
          plugins={[
            dayGridPlugin,
            resourceTimelinePlugin,
            listPlugin,
            interactionPlugin,
          ]}
          resourceAreaWidth="260px"
          resourceOrder={"boomSize"}
          resourceLabelContent={(arg) => {
            return (
              <ResourceLabelContent
                handleVehicleInformationModal={handleVehicleInformationModal}
                arg={arg}
              />
            );
          }}
          resourceLaneDidMount={() => window.dispatchEvent(new Event("resize"))}
          eventStartEditable={true}
          eventResourceEditable={true}
          resources={resources}
          schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
          slotLabelDidMount={(arg) => {
            const columnHeaderCell = arg.el;
            columnHeaderCell.style.fontWeight = "lighter";
          }}
          nowIndicator={true}
          views={{
            resourceTimelineDay: {
              slotDuration: "00:30:00",
              slotLabelInterval: "01:00:00",
              slotLabelFormat: (arg) => {
                const startDate = arg.start.marker;
                const formattedDate = new Intl.DateTimeFormat("nl-BE", {
                  timeZone: "UTC",
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: false,
                }).format(startDate);
                return formattedDate;
              },
              eventContent: (arg: EventContentArg) => {
                if (arg.event.display === "background") {
                  return null;
                }
                return <EventContentDay arg={arg} eventsNumber={1} />;
              },
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
              slotMinWidth: 35,
              nowIndicator: true,
            },
            resourceTimelineWeek: {
              slotLabelInterval: { days: 1 },
              slotLabelFormat: (arg) =>
                formatCalendarDate(
                  arg.start.marker,
                  calendarLocales[selectedLanguage]
                ),
              eventContent: dayEventContent,
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
              nowIndicator: true,
            },
            resourceTimelineMonth: {
              type: "resourceTimeline",
              duration: { months: 1 },
              buttonText: t("common:month"),
              slotDuration: { days: 1 },
              slotLabelInterval: { days: 1 },
              slotLabelFormat: (arg) =>
                formatCalendarDate(
                  arg.start.marker,
                  calendarLocales[selectedLanguage]
                ),
              eventContent: dayEventContent,
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
              slotMinWidth: 150,
            },
            listMonth: {
              buttonText: t("common:list"),
              eventContent: listMonthContent,
              viewDidMount: () =>
                setTimeout(
                  () => window.dispatchEvent(new Event("resize")),
                  500
                ),
            },
          }}
        />

        {reservation ? (
          <EventContentModal
            open={openJobEventModal}
            onClose={handleCloseJobEventModal}
            handleOpenReservationModalCancel={handleOpenReservationModalCancel}
            jobCancelled={confirmJobCancelled}
            reservation={reservation}
            onEditReservationClick={openEditReservationModal}
            onNavigateToReservationDetailsClick={
              handleNavigateToReservationDetails
            }
          />
        ) : null}

        <VehicleInformationModal
          vehicleId={vehicleInformationValues.vehicleId}
        />

        <ReservationModal
          initialFormValues={reservationFormValues}
          isLoading={isLoading}
          handleSubmitReservationModal={(values) =>
            handleSubmitReservationModal(values)
          }
          handleCloseReservationModal={handleCloseReservationModal}
        />

        <ReservationCancelWithCommentModal
          onSubmit={handleCancelReservation}
          isLoading={isLoading}
          flow={reservationCancelValues.flow}
          cancelationFee={reservationCancelValues.cancelationFee}
          reservationTitle={reservationCancelValues.reservationTitle}
          handleCloseReservationModalCancel={handleCloseReservationModalCancel}
        />

        <ConfirmReservationModal
          isOpen={isConfirmModalOpen}
          handleClose={handleCloseConfirmModal}
          handleConfirm={handleConfirmReservation}
        />
        <ReservationEditModal
          initialFormValues={reservationEditValues}
          handleEditReservation={handleEditReservation}
          isLoading={isLoading}
          flow={reservationEditValues.flow}
          handleCloseReservationEditModal={handleCloseReservationEditModal}
          fullUpdate
        />
      </CePaper>
    </>
  );
};
